// Global Button Styles for Professional Look
// Ensures all buttons have consistent borders and hover effects

// Base button styling
.ant-btn {
  // Ensure all buttons have borders by default
  &:not(.ant-btn-link):not(.ant-btn-text) {
    transition: all 0.2s ease;
  }


  // Danger button styling
  &.ant-btn-dangerous {
    background-color: #fff;
    border-color: #ff4d4f !important;
    color: #ff4d4f;

    &:hover {
      background-color: #fff2f0;
      border-color: #ff7875 !important;
      color: #ff7875;
    }

    &:focus {
      background-color: #fff2f0;
      border-color: #cf1322 !important;
      color: #cf1322;
    }
  }

  // Ghost button styling
  &.ant-btn-ghost {
    background-color: transparent;
    border-color: #d9d9d9 !important;
    color: #595959;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
      border-color: #40a9ff !important;
      color: #40a9ff;
    }

    &:focus {
      background-color: rgba(0, 0, 0, 0.04);
      border-color: #1890ff !important;
      color: #1890ff;
    }
  }
}

// Action button styles for tables
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;

  .action-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 16px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }

    &:active {
      opacity: 0.6;
    }

    // Statistics button
    &.action-btn-statistics {
      background-color: #f0f5ff;
      color: #1890ff;
      border-color: #d6e4ff;

      &:hover {
        background-color: #d6e4ff;
        color: #096dd9;
        border-color: #91d5ff;
      }
    }

    // Edit button
    &.action-btn-edit {
      background-color: #f6ffed;
      color: #52c41a;
      border-color: #d9f7be;

      &:hover {
        background-color: #d9f7be;
        color: #389e0d;
        border-color: #95de64;
      }
    }

    // Delete button
    &.action-btn-delete {
      background-color: #fff2f0;
      color: #ff4d4f;
      border-color: #ffccc7;

      &:hover {
        background-color: #ffccc7;
        color: #cf1322;
        border-color: #ff7875;
      }
    }

    // View button
    &.action-btn-view {
      background-color: #f9f0ff;
      color: #722ed1;
      border-color: #efdbff;

      &:hover {
        background-color: #f4e6ff;
        color: #531dab;
        border-color: #d3adf7;
      }
    }

    // Info button
    &.action-btn-info {
      background-color: #e6f7ff;
      color: #1890ff;
      border-color: #91d5ff;

      &:hover {
        background-color: #bae7ff;
        color: #096dd9;
        border-color: #69c0ff;
      }
    }

    // Warning button
    &.action-btn-warning {
      background-color: #fffbe6;
      color: #faad14;
      border-color: #ffe58f;

      &:hover {
        background-color: #fff7e6;
        color: #d48806;
        border-color: #ffd666;
      }
    }
  }
}

// Form filter button styles
.form-filter {
  .filter-buttons,
  .search-buttons {
    .ant-btn {
      min-width: 100px;
      height: 40px;
      border-radius: 6px;
      font-weight: 500;
    }
  }
}

// Admin filter buttons styles
.admin-filter-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .ant-btn {
    min-width: 100px;
    height: 40px;
    border-radius: 6px;
    font-weight: 500;

    // Ensure ghost-white buttons have visible borders in filter context
    &.ant-btn-ghost-white {
      border-color: var(--navy) !important;

      &:hover {
        border-color: var(--navy) !important;
      }

      &:focus {
        border-color: var(--navy) !important;
      }
    }
  }
}

// Large button styles for headers
.header-buttons {
  .ant-btn {
    height: 40px;
    border-radius: 6px;
    font-weight: 500;
    min-width: 100px;
  }
}

// Responsive button adjustments
@media (max-width: 768px) {
  .action-buttons {
    .action-btn {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }
  }

  .form-filter {
    .filter-buttons,
    .search-buttons {
      .ant-btn {
        min-width: 80px;
        height: 36px;
      }
    }
  }

  .admin-filter-buttons {
    .ant-btn {
      min-width: 80px;
      height: 36px;
    }
  }
}

@media (max-width: 576px) {
  .form-filter {
    .filter-buttons,
    .search-buttons {
      flex-direction: column;
      gap: 8px;

      .ant-btn {
        width: 100%;
        min-width: auto;
      }
    }
  }

  .admin-filter-buttons {
    flex-direction: column;
    gap: 8px;

    .ant-btn {
      width: 100%;
      min-width: auto;
    }
  }
}
