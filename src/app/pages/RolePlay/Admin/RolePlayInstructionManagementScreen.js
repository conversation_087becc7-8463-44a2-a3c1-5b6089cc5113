import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { Card, Form, Input, Row, Col, Tooltip, Select } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined, CalendarOutlined } from "@ant-design/icons";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";
import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, navigateAfterDelete, orderColumn, paginationConfig, handleReplaceUrlSearch, formatTimeDate } from "@common/functionCommons";

import { getInstructionPagination, deleteInstruction } from "@services/RolePlay/RolePlayInstructionService";

import "./RolePlayInstructionManagementScreen.scss";

// Định nghĩa các loại mô phỏng
const SIMULATION_TYPES = ['Sale', 'Service', 'HR', 'Education', 'Other'];

const RolePlayInstructionManagementScreen = ({ ...props }) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();

  const [instructionData, setInstructionData] = useState(PAGINATION_INIT);
  const [isLoading, setLoading] = useState(false);

  const [formFilter] = Form.useForm();

  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getInstructionData(paging, query);
  }, [location.search]);

  const getInstructionData = async (paging = instructionData.paging, query = instructionData.query) => {
    setLoading(true);
    try {
      const dataResponse = await getInstructionPagination(paging, query);
      if (dataResponse) {
        setInstructionData(handlePagingData(dataResponse, query));
      }
    } catch (error) {
      console.error("Error fetching instructions:", error);
      toast.error(t("ERROR_FETCHING_INSTRUCTIONS"));
    } finally {
      setLoading(false);
    }
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, instructionData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, instructionData.paging.pageSize, {});
  };

  const handleDelete = (instructionId, instructionName) => {
    confirm.delete({
      title: t("DELETE_INSTRUCTION"),
      content: t("DELETE_INSTRUCTION_CONFIRM", { name: instructionName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setLoading(true);
        try {
          const apiResponse = await deleteInstruction(instructionId);
          if (apiResponse) {
            toast.success(t("DELETE_INSTRUCTION_SUCCESS"));
            navigateAfterDelete(instructionData, getInstructionData);
          } else {
            toast.error(t("DELETE_INSTRUCTION_ERROR"));
          }
        } catch (error) {
          console.error("Error deleting instruction:", error);
          toast.error(t("DELETE_INSTRUCTION_ERROR"));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // Định nghĩa các cột cho bảng
  const columns = [
    {
      ...orderColumn(instructionData.paging),
      width: 80,
    },
    {
      title: t("NAME"),
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (text) => <span className="name-value">{text}</span>,
    },
    {
      title: t("SIMULATION_TYPE"),
      dataIndex: "simulationType",
      key: "simulationType",
      width: 150,
      render: (text) => text,
    },
    {
      title: t("PERSONA_INSTRUCTION"),
      dataIndex: "personaInstruction",
      key: "personaInstruction",
      width: 300,
      render: (text) => text,
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
    {
      title: t("ACTION"),
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="action-buttons">
          <Tooltip title={t("EDIT_INSTRUCTION")}>
            <Link to={LINK.ADMIN.ROLE_PLAY_INSTRUCTION_DETAIL.format(record._id)}>
              <div className="action-btn action-btn-edit">
                <EditOutlined />
              </div>
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_INSTRUCTION")}>
            <div
              className="action-btn action-btn-delete"
              onClick={() => handleDelete(record?._id, record?.name)}
            >
              <DeleteIcon />
            </div>
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(instructionData.paging, instructionData.query, i18n.language);

  return (
    <Loading active={isLoading} transparent>
      <div className="roleplay-instruction-container">
        <Card className="roleplay-instruction-info-card">
          <div className="roleplay-instruction-info-header">
            <div>
              <h1 className="roleplay-instruction-title">{t("ROLEPLAY_INSTRUCTION_MANAGEMENT")}</h1>
              <p className="roleplay-instruction-description">{t("ROLEPLAY_INSTRUCTION_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN.ROLE_PLAY_INSTRUCTION_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_INSTRUCTION")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="roleplay-instruction-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={12}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_INSTRUCTION_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={12}>
                    <AntForm.Item name="simulationType" className="search-form-item">
                      <Select
                        placeholder={t("FILTER_BY_SIMULATION_TYPE")}
                        allowClear
                        style={{ width: '100%' }}
                      >
                        {SIMULATION_TYPES.map(type => (
                          <Select.Option key={type} value={type}>
                            {type}
                          </Select.Option>
                        ))}
                      </Select>
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="admin-filter-buttons">
                  <AntButton
                    type={BUTTON.GHOST_WHITE}
                    size="large"
                    onClick={onClearFilter}
                  >
                    {t("CLEAR_FILTER")}
                  </AntButton>
                  <AntButton
                    type={BUTTON.DEEP_NAVY}
                    size="large"
                    htmlType="submit"
                  >
                    {t("SEARCH_FILTER")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="roleplay-instruction-table-card">
          <TableAdmin
            columns={columns}
            dataSource={instructionData.rows}
            pagination={{ ...pagination }}
            scroll={{ x: 1000 }}
            className={"roleplay-instruction-table"}
            rowClassName={() => "roleplay-instruction-table-row"}
            locale={{ emptyText: t("NO_INSTRUCTIONS_FOUND") }}
            rowKey="_id"
          />
        </Card>
      </div>
    </Loading>
  );
};

export default RolePlayInstructionManagementScreen;
