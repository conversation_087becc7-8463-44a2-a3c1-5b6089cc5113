# Trang Quản lý Người dùng

## 📋 Tổng quan

Trang quản lý người dùng được xây dựng tại route `/admin/users` với đầy đủ các chức năng CRUD và tìm kiếm, lọ<PERSON> dữ liệu theo yêu cầu.

## 🚀 Tính năng chính

### ✅ Đã hoàn thành:

1. **Hiển thị danh sách người dùng**
   - Bảng hiển thị với phân trang
   - Các cột: <PERSON><PERSON>ê<PERSON>, <PERSON>ail, SĐT, <PERSON><PERSON> trò, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>á<PERSON>, <PERSON><PERSON><PERSON> t<PERSON>, <PERSON><PERSON>nh động
   - Responsive design

2. **Tìm kiếm và lọc**
   - Tìm kiếm theo tên, email
   - Lọc theo vai trò (Admin, Normal, Contributor)
   - Lọc theo loại ngườ<PERSON> dùng (Student, Teacher)
   - Lọ<PERSON> theo trạng thái (Active/Inactive)
   - <PERSON><PERSON><PERSON> xóa bộ lọc

3. **Thêm người dùng mới**
   - Modal form với validation đầy đủ
   - Các trường: Email, <PERSON>ọ tên, SĐT, Giới tính, Vai trò, Loại, Trạng thái
   - Validation email format và số điện thoại

4. **Chỉnh sửa người dùng**
   - Modal form tương tự như thêm mới
   - Email không thể chỉnh sửa
   - Pre-fill dữ liệu hiện tại

5. **Xóa người dùng**
   - Modal xác nhận trước khi xóa
   - Soft delete (không xóa vĩnh viễn)
   - Bảo vệ System Admin (không thể xóa)

6. **UI/UX**
   - Loading states
   - Error handling với toast notifications
   - Success messages
   - Responsive design
   - Icon và badge cho trạng thái

## 🔐 Phân quyền

- **System Admin**: Toàn quyền truy cập
- **Regular Admin**: Có quyền truy cập trang quản lý người dùng
- **Teacher**: Không có quyền truy cập (có thể cấu hình thêm nếu cần)

## 📁 Cấu trúc file

```
src/app/pages/AdminPage/UserManagement/
├── index.js                          # Component chính
├── UserManagement.scss               # Styles chính
├── README.md                         # Tài liệu này
└── components/
    ├── UserFilters/
    │   ├── index.js                  # Component filter và search
    │   └── UserFilters.scss          # Styles cho filters
    ├── UserFormModal/
    │   ├── index.js                  # Modal form thêm/sửa
    │   └── UserFormModal.scss        # Styles cho form modal
    └── UserDeleteConfirmModal/
        └── index.js                  # Modal xác nhận xóa
```

## 🔧 Service API

Service được tạo tại `src/app/services/UserManagement/index.js` với các method:

- `getUsers(params)` - Lấy danh sách với phân trang
- `createUser(userData)` - Tạo người dùng mới
- `updateUser(id, userData)` - Cập nhật thông tin
- `deleteUser(id)` - Xóa người dùng (soft delete)
- `getUserById(id)` - Lấy chi tiết một người dùng
- `getUserByEmail(email)` - Tìm theo email
- `getAllUsers(params)` - Lấy tất cả (không phân trang)

## 🛠 Cách sử dụng

### 1. Truy cập trang
Điều hướng đến `/admin/users` hoặc click vào menu "Quản lý người dùng" trong admin panel.

### 2. Tìm kiếm và lọc
- Nhập từ khóa vào ô tìm kiếm để tìm theo tên hoặc email
- Sử dụng các dropdown để lọc theo vai trò, loại, trạng thái
- Click "Xóa bộ lọc" để reset tất cả filter

### 3. Thêm người dùng mới
- Click nút "Thêm người dùng"
- Điền thông tin vào form (Email và Họ tên là bắt buộc)
- Click "Tạo mới" để lưu

### 4. Chỉnh sửa người dùng
- Click icon edit (✏️) ở cột hành động
- Chỉnh sửa thông tin cần thiết
- Click "Cập nhật" để lưu

### 5. Xóa người dùng
- Click icon delete (🗑️) ở cột hành động
- Xác nhận trong modal popup
- Lưu ý: System Admin không thể bị xóa

## 🎨 Customization

### Thêm cột mới vào bảng
Chỉnh sửa array `columns` trong file `index.js`:

```javascript
const columns = [
  // ... existing columns
  {
    title: t('Cột mới'),
    dataIndex: 'newField',
    key: 'newField',
    width: 150,
    render: (value) => value || '-',
  },
];
```

### Thêm filter mới
Chỉnh sửa component `UserFilters` để thêm dropdown hoặc input mới.

### Thêm validation
Chỉnh sửa `UserFormModal` để thêm rules validation mới:

```javascript
<AntForm.Item
  name="newField"
  label={t('Trường mới')}
  rules={[
    RULE.REQUIRED,
    { validator: customValidator }
  ]}
>
  <Input placeholder={t('Nhập giá trị')} />
</AntForm.Item>
```

## 🐛 Troubleshooting

### Lỗi không load được danh sách
- Kiểm tra API endpoint `/api/users/list`
- Kiểm tra authentication token
- Xem console để debug lỗi

### Lỗi không tạo được người dùng
- Kiểm tra validation form
- Kiểm tra API endpoint `/api/users`
- Đảm bảo email chưa tồn tại

### Lỗi permission
- Kiểm tra user có role admin
- Kiểm tra file `adminPermissions.js`
- Đảm bảo route được thêm vào `REGULAR_ADMIN_ALLOWED`

## 📚 Tài liệu tham khảo

- API Guide: `src/app/pages/AdminPage/USER_MANAGEMENT_API_GUIDE.md`
- Admin Permissions: `src/utils/adminPermissions.js`
- Routing: `src/app/routing/AdminPageRouter.js`
- Constants: `src/constants/link.js`
