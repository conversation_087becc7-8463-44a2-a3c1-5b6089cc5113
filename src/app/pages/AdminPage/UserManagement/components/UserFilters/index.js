import React, { useState, useCallback } from 'react';
import { Input, Select, Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import AntButton from '@component/AntButton';
import { BUTTON } from '@constant';
import SearchIcon from '@component/SvgIcons/SearchIcon';
import './UserFilters.scss';

const { Option } = Select;

const UserFilters = ({ 
  onFiltersChange, 
  loading = false,
  initialFilters = {}
}) => {
  const { t } = useTranslation();
  
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    type: '',
    active: '',
    ...initialFilters
  });

  const handleFilterChange = useCallback((key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  }, [filters, onFiltersChange]);

  const handleSearchChange = useCallback((e) => {
    handleFilterChange('search', e.target.value);
  }, [handleFilterChange]);

  const handleRoleChange = useCallback((value) => {
    handleFilterChange('role', value);
  }, [handleFilterChange]);

  const handleTypeChange = useCallback((value) => {
    handleFilterChange('type', value);
  }, [handleFilterChange]);

  const handleActiveChange = useCallback((value) => {
    handleFilterChange('active', value);
  }, [handleFilterChange]);

  const handleClearFilters = useCallback(() => {
    const clearedFilters = {
      search: '',
      role: '',
      type: '',
      active: ''
    };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  }, [onFiltersChange]);

  const hasActiveFilters = filters.search || filters.role || filters.type || filters.active;

  return (
    <div className="user-filters">
      <Row gutter={[16, 16]} align="middle">
        {/* Search Input */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Input
            placeholder={t('Tìm kiếm theo tên, email...')}
            value={filters.search}
            onChange={handleSearchChange}
            prefix={<SearchIcon />}
            allowClear
            disabled={loading}
            size="large"
          />
        </Col>

        {/* Role Filter */}
        <Col xs={24} sm={12} md={6} lg={4}>
          <Select
            placeholder={t('Vai trò')}
            value={filters.role || undefined}
            onChange={handleRoleChange}
            allowClear
            disabled={loading}
            size="large"
            style={{ width: '100%' }}
          >
            <Option value="admin">{t('Quản trị viên')}</Option>
            <Option value="normal">{t('Người dùng thông thường')}</Option>
            <Option value="contributor">{t('Người đóng góp')}</Option>
          </Select>
        </Col>

        {/* Type Filter */}
        <Col xs={24} sm={12} md={6} lg={4}>
          <Select
            placeholder={t('Loại người dùng')}
            value={filters.type || undefined}
            onChange={handleTypeChange}
            allowClear
            disabled={loading}
            size="large"
            style={{ width: '100%' }}
          >
            <Option value="student">{t('Học viên')}</Option>
            <Option value="teacher">{t('Giảng viên')}</Option>
          </Select>
        </Col>

        {/* Active Status Filter */}
        <Col xs={24} sm={12} md={6} lg={4}>
          <Select
            placeholder={t('Trạng thái')}
            value={filters.active || undefined}
            onChange={handleActiveChange}
            allowClear
            disabled={loading}
            size="large"
            style={{ width: '100%' }}
          >
            <Option value="true">{t('Đã kích hoạt')}</Option>
            <Option value="false">{t('Chưa kích hoạt')}</Option>
          </Select>
        </Col>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <Col xs={24} sm={12} md={6} lg={4}>
            <AntButton
              type={BUTTON.WHITE}
              onClick={handleClearFilters}
              disabled={loading}
              size="large"
              style={{ width: '100%' }}
            >
              {t('Xóa bộ lọc')}
            </AntButton>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default UserFilters;
