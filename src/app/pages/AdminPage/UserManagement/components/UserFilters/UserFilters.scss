.user-filters {
  background: var(--background-light-background-1, #ffffff);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid var(--background-light-background-grey, #f0f0f0);

  .ant-input {
    border-radius: 6px;
    border: 1px solid var(--background-light-background-grey, #d9d9d9);
    
    &:focus,
    &:hover {
      border-color: var(--primary-colours-blue-light-2, #40a9ff);
    }
  }

  .ant-select {
    .ant-select-selector {
      border-radius: 6px;
      border: 1px solid var(--background-light-background-grey, #d9d9d9);
      
      &:focus,
      &:hover {
        border-color: var(--primary-colours-blue-light-2, #40a9ff);
      }
    }

    &.ant-select-focused {
      .ant-select-selector {
        border-color: var(--primary-colours-blue-light-2, #40a9ff);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding: 16px;
    margin-bottom: 16px;
  }
}
