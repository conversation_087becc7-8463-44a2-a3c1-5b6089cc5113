import React from 'react';
import { useTranslation } from 'react-i18next';
import AntModal from '@component/AntModal';
import { CONSTANT } from '@constant';

const UserDeleteConfirmModal = ({
  open,
  onCancel,
  onConfirm,
  user = null,
  loading = false
}) => {
  const { t } = useTranslation();

  if (!user) return null;

  const handleConfirm = () => {
    onConfirm(user._id);
  };

  const isSystemAdmin = user.isSystemAdmin || user.role === 'admin';

  return (
    <AntModal
      title={t('Xác nhận xóa người dùng')}
      open={open}
      onCancel={onCancel}
      onOk={isSystemAdmin ? undefined : handleConfirm}
      confirmLoading={loading}
      okText={isSystemAdmin ? undefined : t('Xóa')}
      cancelText={t(isSystemAdmin ? 'Đóng' : 'Hủy')}
      align={CONSTANT.CENTER}
      footerAlign={CONSTANT.CENTER}
      width={480}
      footerless={isSystemAdmin}
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        {isSystemAdmin ? (
          <>
            <div style={{ marginBottom: 16, fontSize: 16, fontWeight: 500 }}>
              {t('Không thể xóa người dùng này')}
            </div>
            <div style={{ color: '#ff4d4f', marginBottom: 16 }}>
              {t('Người dùng này là quản trị viên hệ thống và không thể bị xóa.')}
            </div>
            <div style={{ marginBottom: 8 }}>
              <strong>{t('Tên')}:</strong> {user.fullName}
            </div>
            <div style={{ marginBottom: 8 }}>
              <strong>{t('Email')}:</strong> {user.email}
            </div>
            <div>
              <strong>{t('Vai trò')}:</strong> {user.role === 'admin' ? t('Quản trị viên') : t('Quản trị hệ thống')}
            </div>
          </>
        ) : (
          <>
            <div style={{ marginBottom: 16, fontSize: 16, fontWeight: 500 }}>
              {t('Bạn có chắc chắn muốn xóa người dùng này?')}
            </div>
            <div style={{ color: '#faad14', marginBottom: 16 }}>
              {t('Hành động này sẽ thực hiện soft delete. Người dùng sẽ không thể đăng nhập nhưng dữ liệu vẫn được lưu trữ.')}
            </div>
            <div style={{ marginBottom: 8 }}>
              <strong>{t('Tên')}:</strong> {user.fullName}
            </div>
            <div style={{ marginBottom: 8 }}>
              <strong>{t('Email')}:</strong> {user.email}
            </div>
            <div style={{ marginBottom: 8 }}>
              <strong>{t('Vai trò')}:</strong> {
                user.role === 'admin' ? t('Quản trị viên') :
                user.role === 'contributor' ? t('Người đóng góp') :
                t('Người dùng thông thường')
              }
            </div>
            {user.type && (
              <div>
                <strong>{t('Loại')}:</strong> {
                  user.type === 'teacher' ? t('Giảng viên') : t('Học viên')
                }
              </div>
            )}
          </>
        )}
      </div>
    </AntModal>
  );
};

export default UserDeleteConfirmModal;
