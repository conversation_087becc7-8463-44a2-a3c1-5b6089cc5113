.user-form-modal {
  .modal-body {
    padding: 24px;
  }

  .user-form {
    .ant-form-item {
      margin-bottom: 20px;

      .ant-form-item-label {
        padding-bottom: 4px;

        label {
          font-weight: 500;
          color: var(--typo-colours-text-primary, #262626);
        }
      }

      .ant-input,
      .ant-select-selector {
        border-radius: 6px;
        border: 1px solid var(--background-light-background-grey, #d9d9d9);
        
        &:focus,
        &:hover {
          border-color: var(--primary-colours-blue-light-2, #40a9ff);
        }
      }

      .ant-select {
        &.ant-select-focused {
          .ant-select-selector {
            border-color: var(--primary-colours-blue-light-2, #40a9ff);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }

      .ant-switch {
        &.ant-switch-checked {
          background-color: var(--primary-colours-blue-light-2, #1890ff);
        }
      }

      // Error state
      &.ant-form-item-has-error {
        .ant-input,
        .ant-select-selector {
          border-color: var(--error-color, #ff4d4f);
        }
      }
    }

    // Required field indicator
    .ant-form-item-required {
      &::before {
        content: '*';
        color: var(--error-color, #ff4d4f);
        margin-right: 4px;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .modal-body {
      padding: 16px;
    }

    .user-form {
      .ant-col {
        &:not(:last-child) {
          margin-bottom: 0;
        }
      }
    }
  }
}
