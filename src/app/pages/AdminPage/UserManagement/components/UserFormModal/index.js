import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Switch, Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import AntModal from '@component/AntModal';
import { AntForm } from '@component/AntForm';
import RULE from '@src/constants/rule';
import './UserFormModal.scss';

const { Option } = Select;

const UserFormModal = ({
  open,
  onCancel,
  onSubmit,
  user = null,
  loading = false
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  const isEditing = !!user;

  useEffect(() => {
    if (open) {
      if (isEditing && user) {
        // Populate form with user data for editing
        form.setFieldsValue({
          email: user.email,
          fullName: user.fullName,
          phone: user.phone || '',
          gender: user.gender || '',
          role: user.role || 'normal',
          type: user.type || '',
          active: user.active || false,
          isSystemAdmin: user.isSystemAdmin || false
        });
      } else {
        // Reset form for creating new user
        form.resetFields();
        form.setFieldsValue({
          role: 'normal',
          active: false,
          isSystemAdmin: false
        });
      }
    }
  }, [open, isEditing, user, form]);

  const handleSubmit = async (values) => {
    try {
      setSubmitting(true);
      await onSubmit(values);
      form.resetFields();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const validateEmail = async (_, value) => {
    if (!value) return Promise.resolve();

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return Promise.reject(new Error(t('Email không hợp lệ')));
    }
    return Promise.resolve();
  };

  const validatePhone = async (_, value) => {
    if (!value) return Promise.resolve();

    const phoneRegex = /^[0-9]{10,11}$/;
    if (!phoneRegex.test(value)) {
      return Promise.reject(new Error(t('Số điện thoại không hợp lệ')));
    }
    return Promise.resolve();
  };

  return (
    <AntModal
      title={isEditing ? t('Chỉnh sửa người dùng') : t('Thêm người dùng mới')}
      open={open}
      onCancel={handleCancel}
      onOk={() => form.submit()}
      confirmLoading={loading || submitting}
      submitting={submitting}
      formId="user-form"
      okText={isEditing ? t('Cập nhật') : t('Tạo mới')}
      cancelText={t('Hủy')}
      width={600}
      className="user-form-modal"
    >
      <AntForm
        form={form}
        id="user-form"
        layout="vertical"
        onFinish={handleSubmit}
        className="user-form"
      >
        <Row gutter={16}>
          {/* Email */}
          <Col span={24}>
            <AntForm.Item
              name="email"
              label={t('Email')}
              rules={[
                RULE.REQUIRED,
                { validator: validateEmail }
              ]}
            >
              <Input
                placeholder={t('Nhập email')}
                disabled={isEditing} // Email không thể sửa khi edit
                size="large"
              />
            </AntForm.Item>
          </Col>

          {/* Full Name */}
          <Col span={24}>
            <AntForm.Item
              name="fullName"
              label={t('Họ và tên')}
              rules={[RULE.REQUIRED]}
            >
              <Input
                placeholder={t('Nhập họ và tên')}
                size="large"
              />
            </AntForm.Item>
          </Col>

          {/* Phone */}
          <Col span={12}>
            <AntForm.Item
              name="phone"
              label={t('Số điện thoại')}
              rules={[{ validator: validatePhone }]}
            >
              <Input
                placeholder={t('Nhập số điện thoại')}
                size="large"
              />
            </AntForm.Item>
          </Col>

          {/* Gender */}
          <Col span={12}>
            <AntForm.Item
              name="gender"
              label={t('Giới tính')}
            >
              <Select
                placeholder={t('Chọn giới tính')}
                allowClear
                size="large"
              >
                <Option value="male">{t('Nam')}</Option>
                <Option value="female">{t('Nữ')}</Option>
                <Option value="other">{t('Khác')}</Option>
              </Select>
            </AntForm.Item>
          </Col>

          {/* Role */}
          <Col span={12}>
            <AntForm.Item
              name="role"
              label={t('Vai trò')}
              rules={[RULE.REQUIRED]}
            >
              <Select
                placeholder={t('Chọn vai trò')}
                size="large"
              >
                <Option value="normal">{t('Người dùng thông thường')}</Option>
                <Option value="admin">{t('Quản trị viên')}</Option>
                <Option value="contributor">{t('Người đóng góp')}</Option>
              </Select>
            </AntForm.Item>
          </Col>

          {/* Type */}
          <Col span={12}>
            <AntForm.Item
              name="type"
              label={t('Loại người dùng')}
            >
              <Select
                placeholder={t('Chọn loại người dùng')}
                allowClear
                size="large"
              >
                <Option value="student">{t('Học viên')}</Option>
                <Option value="teacher">{t('Giảng viên')}</Option>
              </Select>
            </AntForm.Item>
          </Col>

          {/* Active Status */}
          <Col span={12}>
            <AntForm.Item
              name="active"
              label={t('Trạng thái kích hoạt')}
              valuePropName="checked"
            >
              <Switch
                checkedChildren={t('Đã kích hoạt')}
                unCheckedChildren={t('Chưa kích hoạt')}
              />
            </AntForm.Item>
          </Col>

          {/* System Admin */}
          <Col span={12}>
            <AntForm.Item
              name="isSystemAdmin"
              label={t('Quản trị hệ thống')}
              valuePropName="checked"
            >
              <Switch
                checkedChildren={t('Có')}
                unCheckedChildren={t('Không')}
              />
            </AntForm.Item>
          </Col>
        </Row>
      </AntForm>
    </AntModal>
  );
};

export default UserFormModal;
