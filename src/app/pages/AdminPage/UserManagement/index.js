import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Tag, Space, Tooltip } from 'antd';
import { toast } from '@component/ToastProvider';
import AntButton from '@component/AntButton';
import TableAdmin from '@component/TableAdmin';
import { BUTTON } from '@constant';
import { formatDate } from '@common/functionCommons';

// Import components
import UserFilters from './components/UserFilters';
import UserFormModal from './components/UserFormModal';
import UserDeleteConfirmModal from './components/UserDeleteConfirmModal';

// Import service
import userManagementService from '@services/UserManagement';

// Import icons
import EditPen from '@component/SvgIcons/Edit/EditPen';
import DeleteIcon from '@component/SvgIcons/DeleteIcon';
import PlusIcon from '@component/SvgIcons/PlusIcon';
import UserIcon from '@component/SvgIcons/User';

import './UserManagement.scss';

const UserManagementScreen = () => {
  const { t } = useTranslation();

  // State management
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100']
  });

  const [filters, setFilters] = useState({
    search: '',
    role: '',
    type: '',
    active: ''
  });

  // Modal states
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [formLoading, setFormLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Load users data
  const loadUsers = useCallback(async (page = 1, pageSize = 10, currentFilters = filters) => {
    try {
      setLoading(true);
      const params = {
        page,
        limit: pageSize,
        ...currentFilters
      };

      const response = await userManagementService.getUsers(params);

      if (response) {
        setUsers(response.docs || []);
        setPagination(prev => ({
          ...prev,
          current: response.page || page,
          pageSize: response.limit || pageSize,
          total: response.totalDocs || 0
        }));
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error(t('Có lỗi xảy ra khi tải danh sách người dùng'));
    } finally {
      setLoading(false);
    }
  }, [filters, t]);

  // Initial load
  useEffect(() => {
    loadUsers();
  }, []);

  // Handle filters change
  const handleFiltersChange = useCallback((newFilters) => {
    setFilters(newFilters);
    loadUsers(1, pagination.pageSize, newFilters);
  }, [pagination.pageSize, loadUsers]);

  // Handle pagination change
  const handleTableChange = useCallback((paginationConfig) => {
    loadUsers(paginationConfig.current, paginationConfig.pageSize);
  }, [loadUsers]);

  // Handle create user
  const handleCreateUser = useCallback(() => {
    setSelectedUser(null);
    setFormModalOpen(true);
  }, []);

  // Handle edit user
  const handleEditUser = useCallback((user) => {
    setSelectedUser(user);
    setFormModalOpen(true);
  }, []);

  // Handle delete user
  const handleDeleteUser = useCallback((user) => {
    setSelectedUser(user);
    setDeleteModalOpen(true);
  }, []);

  // Handle form submit
  const handleFormSubmit = useCallback(async (userData) => {
    try {
      setFormLoading(true);

      if (selectedUser) {
        // Update existing user
        await userManagementService.updateUser(selectedUser._id, userData);
        toast.success(t('Cập nhật người dùng thành công'));
      } else {
        // Create new user
        await userManagementService.createUser(userData);
        toast.success(t('Tạo người dùng mới thành công'));
      }

      setFormModalOpen(false);
      setSelectedUser(null);
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('Error submitting form:', error);
      const errorMessage = error.response?.data?.message ||
        (selectedUser ? t('Có lỗi xảy ra khi cập nhật người dùng') : t('Có lỗi xảy ra khi tạo người dùng'));
      toast.error(errorMessage);
    } finally {
      setFormLoading(false);
    }
  }, [selectedUser, t, loadUsers, pagination.current, pagination.pageSize]);

  // Handle delete confirm
  const handleDeleteConfirm = useCallback(async (userId) => {
    try {
      setDeleteLoading(true);
      await userManagementService.deleteUser(userId);
      toast.success(t('Xóa người dùng thành công'));
      setDeleteModalOpen(false);
      setSelectedUser(null);
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('Error deleting user:', error);
      const errorMessage = error.response?.data?.message || t('Có lỗi xảy ra khi xóa người dùng');
      toast.error(errorMessage);
    } finally {
      setDeleteLoading(false);
    }
  }, [t, loadUsers, pagination.current, pagination.pageSize]);

  // Table columns configuration
  const columns = useMemo(() => [
    {
      title: t('Họ và tên'),
      dataIndex: 'fullName',
      key: 'fullName',
      width: 200,
      render: (text, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <UserIcon style={{ fontSize: 16, color: '#1890ff' }} />
          <span style={{ fontWeight: 500 }}>{text}</span>
        </div>
      ),
    },
    {
      title: t('Email'),
      dataIndex: 'email',
      key: 'email',
      width: 250,
    },
    {
      title: t('Số điện thoại'),
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      render: (text) => text || '-',
    },
    {
      title: t('Vai trò'),
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role) => {
        const roleConfig = {
          admin: { color: 'red', text: t('Quản trị viên') },
          contributor: { color: 'orange', text: t('Người đóng góp') },
          normal: { color: 'blue', text: t('Người dùng') }
        };
        const config = roleConfig[role] || roleConfig.normal;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: t('Loại'),
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => {
        if (!type) return '-';
        const typeConfig = {
          teacher: { color: 'green', text: t('Giảng viên') },
          student: { color: 'cyan', text: t('Học viên') }
        };
        const config = typeConfig[type];
        return config ? <Tag color={config.color}>{config.text}</Tag> : '-';
      },
    },
    {
      title: t('Trạng thái'),
      dataIndex: 'active',
      key: 'active',
      width: 120,
      render: (active) => (
        <Tag color={active ? 'success' : 'default'}>
          {active ? t('Đã kích hoạt') : t('Chưa kích hoạt')}
        </Tag>
      ),
    },
    {
      title: t('Ngày tạo'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date) => formatDate(date, 'DD/MM/YYYY'),
    },
    {
      title: t('Hành động'),
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title={t('Chỉnh sửa')}>
            <AntButton
              type={BUTTON.WHITE}
              size="small"
              icon={<EditPen />}
              onClick={() => handleEditUser(record)}
            />
          </Tooltip>
          <Tooltip title={t('Xóa')}>
            <AntButton
              type={BUTTON.WHITE}
              size="small"
              icon={<DeleteIcon />}
              onClick={() => handleDeleteUser(record)}
              disabled={record.isSystemAdmin}
            />
          </Tooltip>
        </Space>
      ),
    },
  ], [t, handleEditUser, handleDeleteUser]);

  return (
    <div className="user-management-screen">
      {/* Header */}
      <div className="user-management-header">
        <div className="header-content">
          <h1 className="page-title">{t('Quản lý người dùng')}</h1>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            icon={<PlusIcon />}
            onClick={handleCreateUser}
          >
            {t('Thêm người dùng')}
          </AntButton>
        </div>
      </div>

      {/* Filters */}
      <UserFilters
        onFiltersChange={handleFiltersChange}
        loading={loading}
        initialFilters={filters}
      />

      {/* Table */}
      <div className="user-management-table">
        <TableAdmin
          dataSource={users}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          rowKey="_id"
          scroll={{ x: 1200 }}
        />
      </div>

      {/* Modals */}
      <UserFormModal
        open={formModalOpen}
        onCancel={() => setFormModalOpen(false)}
        onSubmit={handleFormSubmit}
        user={selectedUser}
        loading={formLoading}
      />

      <UserDeleteConfirmModal
        open={deleteModalOpen}
        onCancel={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        user={selectedUser}
        loading={deleteLoading}
      />
    </div>
  );
};

export default UserManagementScreen;
