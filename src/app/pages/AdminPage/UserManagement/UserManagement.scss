.user-management-screen {
  padding: 24px;
  background: var(--background-light-background-2, #f5f5f5);
  min-height: 100vh;

  .user-management-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: var(--background-light-background-1, #ffffff);
      padding: 20px 24px;
      border-radius: 8px;
      border: 1px solid var(--background-light-background-grey, #f0f0f0);

      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--typo-colours-text-primary, #262626);
      }
    }
  }

  .user-management-table {
    background: var(--background-light-background-1, #ffffff);
    border-radius: 8px;
    padding: 24px;
    border: 1px solid var(--background-light-background-grey, #f0f0f0);

    .admin-table {
      .ant-table-thead > tr > th {
        background: var(--background-light-background-2, #fafafa);
        font-weight: 600;
        color: var(--typo-colours-text-primary, #262626);
        border-bottom: 2px solid var(--background-light-background-grey, #f0f0f0);
      }

      .ant-table-tbody > tr {
        &:hover > td {
          background: var(--background-light-background-2, #fafafa);
        }

        > td {
          border-bottom: 1px solid var(--background-light-background-grey, #f0f0f0);
          vertical-align: middle;
        }
      }

      // Tag styles
      .ant-tag {
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        padding: 2px 8px;
        border: none;

        &.ant-tag-red {
          background: rgba(255, 77, 79, 0.1);
          color: #ff4d4f;
        }

        &.ant-tag-orange {
          background: rgba(250, 173, 20, 0.1);
          color: #faad14;
        }

        &.ant-tag-blue {
          background: rgba(24, 144, 255, 0.1);
          color: #1890ff;
        }

        &.ant-tag-green {
          background: rgba(82, 196, 26, 0.1);
          color: #52c41a;
        }

        &.ant-tag-cyan {
          background: rgba(19, 194, 194, 0.1);
          color: #13c2c2;
        }

        &.ant-tag-success {
          background: rgba(82, 196, 26, 0.1);
          color: #52c41a;
        }

        &.ant-tag-default {
          background: rgba(0, 0, 0, 0.04);
          color: rgba(0, 0, 0, 0.65);
        }
      }

      // Action buttons
      .ant-btn {
        &.ant-btn-sm {
          height: 28px;
          width: 28px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;

          &:hover {
            background: var(--primary-colours-blue-light-2, #40a9ff);
            border-color: var(--primary-colours-blue-light-2, #40a9ff);
            color: white;

            svg path {
              stroke: white;
            }
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
              background: transparent;
              border-color: var(--background-light-background-grey, #d9d9d9);
              color: rgba(0, 0, 0, 0.25);

              svg path {
                stroke: rgba(0, 0, 0, 0.25);
              }
            }
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 1200px) {
    padding: 16px;

    .user-management-header {
      margin-bottom: 16px;

      .header-content {
        padding: 16px 20px;
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .page-title {
          font-size: 20px;
          text-align: center;
        }
      }
    }

    .user-management-table {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .user-management-header {
      margin-bottom: 12px;

      .header-content {
        padding: 12px 16px;

        .page-title {
          font-size: 18px;
        }
      }
    }

    .user-management-table {
      padding: 12px;

      .admin-table {
        .ant-table {
          font-size: 12px;
        }

        .ant-table-thead > tr > th {
          padding: 8px 4px;
          font-size: 12px;
        }

        .ant-table-tbody > tr > td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
  }
}
