import { Layout } from "antd";
import { Link } from "react-router-dom";

import HeaderAction from "@app/layout/StudentLayout/Header/HeaderAction";
import HeaderStudentMenu from "./HeaderStudentMenu";

import { LINK } from "@link";

import SCBank_logo from "@src/asset/logo/SCBank-logo.svg";

import "./Header.scss";


const Header = ({ ...props }) => {
  return (<>
    <Layout.Header className="student-header">
      <div className="max-w-[1156px] w-[100%] flex justify-between items-center">
        <div className="student-header__left">
          <Link to={LINK.COURSES} className="student-header__logo">
            <img src={SCBank_logo} alt="" />
            {/* <span>Virtual Coach</span> */}
          </Link>
          {/* <HeaderStudentMenu /> */}
        </div>
        <div className="student-header__right">
          <HeaderAction />
        </div>
      </div>
    </Layout.Header>
  </>
  );
};

export default Header;
