import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { useLocation } from "react-router-dom";
import { Link } from "react-router-dom";

import { LINK } from "@link";
import { hasRouteAccess } from "@src/utils/adminPermissions";

import "./Aside.scss";
import { useEffect } from "react";

// Import icons
import TOKEN_INSTRUCTION from "@src/asset/aside/token-instruction.svg";
import TOKEN_INSTRUCTION_ACTIVE from "@src/asset/aside/token-instruction-active.svg";
import COURSE_ICON from "@src/asset/aside/token-instruction.svg";
import COURSE_ICON_ACTIVE from "@src/asset/aside/token-instruction-active.svg";
import AI_PERSONA_ICON from "@src/asset/aside/persona.svg";
import AI_PERSONA_ICON_ACTIVE from "@src/asset/aside/persona-active.svg";
import USER_ICON from "@src/asset/aside/user.svg";
import USER_ICON_ACTIVE from "@src/asset/aside/user-active.svg";

// Define menu items (Settings được đặt cuối cùng)
const ADMIN_MENU_ITEMS = [
  {
    linkTo: LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT,
    title: "COURSE_MANAGEMENT",
    img: COURSE_ICON,
    imgActive: COURSE_ICON_ACTIVE
  },
  {
    linkTo: LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT,
    title: "AI_PERSONA_MANAGEMENT",
    img: AI_PERSONA_ICON,
    imgActive: AI_PERSONA_ICON_ACTIVE
  },
  {
    linkTo: LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT,
    title: "INSTRUCTION_MANAGEMENT",
    img: TOKEN_INSTRUCTION,
    imgActive: TOKEN_INSTRUCTION_ACTIVE
  },
  {
    linkTo: LINK.ADMIN.ROLE_PLAY_STATISTICS_OVERVIEW,
    title: "STATISTICS",
    img: TOKEN_INSTRUCTION,
    imgActive: TOKEN_INSTRUCTION_ACTIVE
  },
  {
    linkTo: LINK.ADMIN.USER_MANAGEMENT,
    title: "USER_MANAGEMENT",
    img: USER_ICON,
    imgActive: USER_ICON_ACTIVE
  },
  {
    linkTo: LINK.ADMIN.SETTING,
    title: "SETTINGS",
    img: TOKEN_INSTRUCTION,
    imgActive: TOKEN_INSTRUCTION_ACTIVE
  }
];

function AdminAside({ user, ...props }) {
  const { t } = useTranslation();
  const location = useLocation();


  // Check if a menu item is active
  const isMenuItemActive = (linkTo) => {
    // Special case: Course statistics page should activate Course Management menu
    if (location.pathname.includes('/admin/role-play-course-statistics/')) {
      return linkTo === LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT;
    }

    return location.pathname === linkTo || location.pathname.startsWith(linkTo + '/');
  };

  // Render a menu item using the same style as regular pages
  const renderMenuItem = (item) => {
    const isActive = isMenuItemActive(item.linkTo);

    return (
      <Link
        key={item.linkTo}
        to={item.linkTo}
        className={`aside-item ${isActive ? 'aside-item__active' : ''}`}
        title={t(item.title)}
      >
        <img
          src={isActive ? item.imgActive : item.img}
          alt=""
          className="aside-item__icon"
        />
        <div className="aside-item__title">
          {t(item.title)}
        </div>
      </Link>
    );
  };

  // Lọc menu items dựa trên quyền của user
  const allowedMenuItems = ADMIN_MENU_ITEMS.filter(item => hasRouteAccess(user, item.linkTo));

  return (
    <div className="aside-body">
      {allowedMenuItems.map(item => renderMenuItem(item))}
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}


export default (connect(mapStateToProps)(AdminAside));
