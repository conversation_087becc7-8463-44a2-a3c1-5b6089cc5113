import axios from 'axios';
import { convertSnakeCaseToCamelCase } from '@src/common/dataConverter';

/**
 * UserManagement Service - Quản lý người dùng
 * Dựa trên API Guide tại src/app/pages/AdminPage/USER_MANAGEMENT_API_GUIDE.md
 */
class UserManagementService {
  constructor() {
    this.baseURL = '/api/users';
  }

  /**
   * L<PERSON>y danh sách người dùng với phân trang
   * @param {Object} params - Tham số query
   * @param {number} params.page - Trang hiện tại (default: 1)
   * @param {number} params.limit - Số items per page (default: 10)
   * @param {string} params.search - Từ khóa tìm kiếm
   * @param {string} params.role - Lọc theo role
   * @param {boolean} params.active - Lọc theo trạng thái active
   * @param {string} params.type - Lọc theo type (student/teacher)
   * @param {string} params.sort - Sắp xếp (default: -createdAt)
   * @returns {Promise} Response data
   */
  async getUsers(params = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        role = '',
        active = '',
        type = '',
        sort = '-createdAt'
      } = params;

      // Xây dựng query object
      const query = {};
      if (role) query.role = role;
      if (active !== '') query.active = active === 'true';
      if (type) query.type = type;

      // Xây dựng query parameters
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sort,
      });

      // Thêm search parameters nếu có
      if (search) {
        queryParams.append('searchFields', 'fullName,email');
        queryParams.append('search', search);
      }

      // Thêm query filter nếu có
      if (Object.keys(query).length > 0) {
        queryParams.append('query', JSON.stringify(query));
      }

      const response = await axios.get(`${this.baseURL}/list?${queryParams.toString()}`);
      
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Tạo người dùng mới
   * @param {Object} userData - Dữ liệu người dùng
   * @param {string} userData.email - Email (required)
   * @param {string} userData.fullName - Họ tên (required)
   * @param {string} userData.phone - Số điện thoại (optional)
   * @param {string} userData.gender - Giới tính (optional)
   * @param {string} userData.role - Vai trò (optional, default: normal)
   * @param {string} userData.type - Loại người dùng (optional)
   * @param {boolean} userData.active - Trạng thái active (optional, default: false)
   * @param {boolean} userData.isSystemAdmin - System admin (optional, default: false)
   * @returns {Promise} Response data
   */
  async createUser(userData) {
    try {
      const response = await axios.post(this.baseURL, userData);
      
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin người dùng
   * @param {string} id - ID người dùng
   * @param {Object} userData - Dữ liệu cập nhật
   * @returns {Promise} Response data
   */
  async updateUser(id, userData) {
    try {
      const response = await axios.put(`${this.baseURL}/${id}`, userData);
      
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Xóa người dùng (soft delete)
   * @param {string} id - ID người dùng
   * @returns {Promise} Response data
   */
  async deleteUser(id) {
    try {
      const response = await axios.delete(`${this.baseURL}/${id}`);
      
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết một người dùng
   * @param {string} id - ID người dùng
   * @returns {Promise} Response data
   */
  async getUserById(id) {
    try {
      const response = await axios.get(`${this.baseURL}/${id}`);
      
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      console.error('Error fetching user by ID:', error);
      throw error;
    }
  }

  /**
   * Tìm người dùng theo email
   * @param {string} email - Email người dùng
   * @returns {Promise} Response data
   */
  async getUserByEmail(email) {
    try {
      const response = await axios.get(`${this.baseURL}/getOneByEmail?email=${encodeURIComponent(email)}`);
      
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      console.error('Error fetching user by email:', error);
      throw error;
    }
  }

  /**
   * Lấy tất cả người dùng (không phân trang)
   * @param {Object} params - Tham số query
   * @returns {Promise} Response data
   */
  async getAllUsers(params = {}) {
    try {
      const {
        search = '',
        role = '',
        active = '',
        type = '',
        sort = '-createdAt'
      } = params;

      // Xây dựng query object
      const query = {};
      if (role) query.role = role;
      if (active !== '') query.active = active === 'true';
      if (type) query.type = type;

      // Xây dựng query parameters
      const queryParams = new URLSearchParams({
        sort,
      });

      // Thêm search parameters nếu có
      if (search) {
        queryParams.append('searchFields', 'fullName,email');
        queryParams.append('search', search);
      }

      // Thêm query filter nếu có
      if (Object.keys(query).length > 0) {
        queryParams.append('query', JSON.stringify(query));
      }

      const response = await axios.get(`${this.baseURL}/findAll?${queryParams.toString()}`);
      
      if (response.status === 200) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      console.error('Error fetching all users:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const userManagementService = new UserManagementService();
export default userManagementService;
