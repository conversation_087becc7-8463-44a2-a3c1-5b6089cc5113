.filter-form {
  display: flex;
  gap: 24px;

  .filter-form__actions {
    margin-bottom: 16px;
    display: flex;
    flex-direction: row;
    gap: 12px; // Consistent with admin-filter-buttons

    button {
      max-width: unset;
      min-width: 100px;
      height: 40px;
      border-radius: 6px;

      // Ensure ghost-white buttons have visible borders in filter context
      &.ant-btn-ghost-white {
        border-color: var(--navy) !important;

        &:hover {
          border-color: var(--navy) !important;
        }

        &:focus {
          border-color: var(--navy) !important;
        }
      }
    }
  }

  .filter-form__date-picker {
    width: 100%;
    padding: 9px 11px !important;
  }
}

// Responsive adjustments for filter form
@media (max-width: 768px) {
  .filter-form {
    .filter-form__actions {
      button {
        min-width: 80px;
        height: 36px;
      }
    }
  }
}

@media (max-width: 576px) {
  .filter-form {
    .filter-form__actions {
      flex-direction: column;
      gap: 8px;

      button {
        width: 100%;
        min-width: auto;
      }
    }
  }
}
